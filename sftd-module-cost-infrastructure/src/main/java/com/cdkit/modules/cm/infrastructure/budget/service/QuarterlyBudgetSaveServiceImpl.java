package com.cdkit.modules.cm.infrastructure.budget.service;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetEntity;
import com.cdkit.modules.cm.domain.budget.service.QuarterlyBudgetSaveService;
import com.cdkit.modules.cm.infrastructure.budget.converter.CostQuarterlyBudgetConverter;
import com.cdkit.modules.cm.infrastructure.budget.entity.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 季度预算保存服务实现
 * 调用基础设施层的ICostQuarterlyBudgetService.saveMain方法来保存主表和子表数据
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class QuarterlyBudgetSaveServiceImpl implements QuarterlyBudgetSaveService {

    private final ICostQuarterlyBudgetService costQuarterlyBudgetService;
    private final CostQuarterlyBudgetConverter costQuarterlyBudgetConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveMainWithDetails(CostQuarterlyBudgetEntity entity) {
        log.info("开始保存季度预算主表和子表数据，预算单号: {}", entity.getQuarterlyBudgetNo());

        try {
            // 转换主表实体
            CostQuarterlyBudget mainEntity = costQuarterlyBudgetConverter.toInfrastructure(entity);

            // 转换子表实体列表
            List<CostQuarterlyBudgetProcPkgDetail> procPkgDetailList = convertProcPkgDetailList(entity.getCostQuarterlyBudgetProcPkgDetailList());
            List<CostQuarterlyBudgetMaterialDetail> materialDetailList = convertMaterialDetailList(entity.getCostQuarterlyBudgetMaterialDetailList());
            List<CostQuarterlyBudgetSubjectDirectCost> subjectDirectCostList = convertSubjectDirectCostList(entity.getCostQuarterlyBudgetSubjectDirectCostList());
            List<CostQuarterlyBudgetCenterIndirectCost> centerIndirectCostList = convertCenterIndirectCostList(entity.getCostQuarterlyBudgetCenterIndirectCostList());
            List<CostQuarterlyBudgetCompMageIndirectCost> compMageIndirectCostList = convertCompMageIndirectCostList(entity.getCostQuarterlyBudgetCompMageIndirectCostList());
            List<CostQuarterlyBudgetNonOptCenterIndirectCost> nonOptCenterIndirectCostList = convertNonOptCenterIndirectCostList(entity.getCostQuarterlyBudgetNonOptCenterIndirectCostList());
            List<CostQuarterlyBudgetRevenueDetail> revenueDetailList = convertRevenueDetailList(entity.getCostQuarterlyBudgetRevenueDetailList());

            // 调用基础设施层服务保存主表和所有子表数据
            costQuarterlyBudgetService.saveMain(mainEntity, procPkgDetailList, materialDetailList, 
                    subjectDirectCostList, centerIndirectCostList, compMageIndirectCostList, 
                    nonOptCenterIndirectCostList, revenueDetailList);

            log.info("保存季度预算主表和子表数据成功，ID: {}, 预算单号: {}", mainEntity.getId(), mainEntity.getQuarterlyBudgetNo());
            return mainEntity.getId();

        } catch (Exception e) {
            log.error("保存季度预算主表和子表数据失败，预算单号: {}", entity.getQuarterlyBudgetNo(), e);
            throw new RuntimeException("保存季度预算数据失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateMainWithDetails(CostQuarterlyBudgetEntity entity) {
        log.info("开始更新季度预算主表和子表数据，ID: {}, 预算单号: {}", entity.getId(), entity.getQuarterlyBudgetNo());

        try {
            // 转换主表实体
            CostQuarterlyBudget mainEntity = costQuarterlyBudgetConverter.toInfrastructure(entity);

            // 转换子表实体列表
            List<CostQuarterlyBudgetProcPkgDetail> procPkgDetailList = convertProcPkgDetailList(entity.getCostQuarterlyBudgetProcPkgDetailList());
            List<CostQuarterlyBudgetMaterialDetail> materialDetailList = convertMaterialDetailList(entity.getCostQuarterlyBudgetMaterialDetailList());
            List<CostQuarterlyBudgetSubjectDirectCost> subjectDirectCostList = convertSubjectDirectCostList(entity.getCostQuarterlyBudgetSubjectDirectCostList());
            List<CostQuarterlyBudgetCenterIndirectCost> centerIndirectCostList = convertCenterIndirectCostList(entity.getCostQuarterlyBudgetCenterIndirectCostList());
            List<CostQuarterlyBudgetCompMageIndirectCost> compMageIndirectCostList = convertCompMageIndirectCostList(entity.getCostQuarterlyBudgetCompMageIndirectCostList());
            List<CostQuarterlyBudgetNonOptCenterIndirectCost> nonOptCenterIndirectCostList = convertNonOptCenterIndirectCostList(entity.getCostQuarterlyBudgetNonOptCenterIndirectCostList());
            List<CostQuarterlyBudgetRevenueDetail> revenueDetailList = convertRevenueDetailList(entity.getCostQuarterlyBudgetRevenueDetailList());

            // 调用基础设施层服务更新主表和所有子表数据
            costQuarterlyBudgetService.updateMain(mainEntity, procPkgDetailList, materialDetailList, 
                    subjectDirectCostList, centerIndirectCostList, compMageIndirectCostList, 
                    nonOptCenterIndirectCostList, revenueDetailList);

            log.info("更新季度预算主表和子表数据成功，ID: {}, 预算单号: {}", mainEntity.getId(), mainEntity.getQuarterlyBudgetNo());
            return mainEntity.getId();

        } catch (Exception e) {
            log.error("更新季度预算主表和子表数据失败，ID: {}, 预算单号: {}", entity.getId(), entity.getQuarterlyBudgetNo(), e);
            throw new RuntimeException("更新季度预算数据失败：" + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteMainWithDetails(String id) {
        log.info("开始删除季度预算主表和子表数据，ID: {}", id);

        try {
            // 调用基础设施层服务删除主表和所有子表数据
            costQuarterlyBudgetService.delMain(id);

            log.info("删除季度预算主表和子表数据成功，ID: {}", id);

        } catch (Exception e) {
            log.error("删除季度预算主表和子表数据失败，ID: {}", id, e);
            throw new RuntimeException("删除季度预算数据失败：" + e.getMessage(), e);
        }
    }

    // ==================== 私有转换方法 ====================

    private List<CostQuarterlyBudgetProcPkgDetail> convertProcPkgDetailList(
            List<com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetProcPkgDetailEntity> domainList) {
        if (domainList == null || domainList.isEmpty()) {
            return new ArrayList<>();
        }
        return domainList.stream()
                .map(this::convertProcPkgDetail)
                .collect(Collectors.toList());
    }

    private CostQuarterlyBudgetProcPkgDetail convertProcPkgDetail(
            com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetProcPkgDetailEntity domainEntity) {
        CostQuarterlyBudgetProcPkgDetail infraEntity = new CostQuarterlyBudgetProcPkgDetail();
        BeanUtils.copyProperties(domainEntity, infraEntity);
        return infraEntity;
    }

    private List<CostQuarterlyBudgetMaterialDetail> convertMaterialDetailList(
            List<com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetMaterialDetailEntity> domainList) {
        if (domainList == null || domainList.isEmpty()) {
            return new ArrayList<>();
        }
        return domainList.stream()
                .map(this::convertMaterialDetail)
                .collect(Collectors.toList());
    }

    private CostQuarterlyBudgetMaterialDetail convertMaterialDetail(
            com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetMaterialDetailEntity domainEntity) {
        CostQuarterlyBudgetMaterialDetail infraEntity = new CostQuarterlyBudgetMaterialDetail();
        BeanUtils.copyProperties(domainEntity, infraEntity);
        return infraEntity;
    }

    private List<CostQuarterlyBudgetSubjectDirectCost> convertSubjectDirectCostList(
            List<com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetSubjectDirectCostEntity> domainList) {
        if (domainList == null || domainList.isEmpty()) {
            return new ArrayList<>();
        }
        return domainList.stream()
                .map(this::convertSubjectDirectCost)
                .collect(Collectors.toList());
    }

    private CostQuarterlyBudgetSubjectDirectCost convertSubjectDirectCost(
            com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetSubjectDirectCostEntity domainEntity) {
        CostQuarterlyBudgetSubjectDirectCost infraEntity = new CostQuarterlyBudgetSubjectDirectCost();
        BeanUtils.copyProperties(domainEntity, infraEntity);
        return infraEntity;
    }

    private List<CostQuarterlyBudgetCenterIndirectCost> convertCenterIndirectCostList(
            List<com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetCenterIndirectCostEntity> domainList) {
        if (domainList == null || domainList.isEmpty()) {
            return new ArrayList<>();
        }
        return domainList.stream()
                .map(this::convertCenterIndirectCost)
                .collect(Collectors.toList());
    }

    private CostQuarterlyBudgetCenterIndirectCost convertCenterIndirectCost(
            com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetCenterIndirectCostEntity domainEntity) {
        CostQuarterlyBudgetCenterIndirectCost infraEntity = new CostQuarterlyBudgetCenterIndirectCost();
        BeanUtils.copyProperties(domainEntity, infraEntity);
        return infraEntity;
    }

    private List<CostQuarterlyBudgetCompMageIndirectCost> convertCompMageIndirectCostList(
            List<com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetCompMageIndirectCostEntity> domainList) {
        if (domainList == null || domainList.isEmpty()) {
            return new ArrayList<>();
        }
        return domainList.stream()
                .map(this::convertCompMageIndirectCost)
                .collect(Collectors.toList());
    }

    private CostQuarterlyBudgetCompMageIndirectCost convertCompMageIndirectCost(
            com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetCompMageIndirectCostEntity domainEntity) {
        CostQuarterlyBudgetCompMageIndirectCost infraEntity = new CostQuarterlyBudgetCompMageIndirectCost();
        BeanUtils.copyProperties(domainEntity, infraEntity);
        return infraEntity;
    }

    private List<CostQuarterlyBudgetNonOptCenterIndirectCost> convertNonOptCenterIndirectCostList(
            List<com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetNonOptCenterIndirectCostEntity> domainList) {
        if (domainList == null || domainList.isEmpty()) {
            return new ArrayList<>();
        }
        return domainList.stream()
                .map(this::convertNonOptCenterIndirectCost)
                .collect(Collectors.toList());
    }

    private CostQuarterlyBudgetNonOptCenterIndirectCost convertNonOptCenterIndirectCost(
            com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetNonOptCenterIndirectCostEntity domainEntity) {
        CostQuarterlyBudgetNonOptCenterIndirectCost infraEntity = new CostQuarterlyBudgetNonOptCenterIndirectCost();
        BeanUtils.copyProperties(domainEntity, infraEntity);
        return infraEntity;
    }

    private List<CostQuarterlyBudgetRevenueDetail> convertRevenueDetailList(
            List<com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetRevenueDetailEntity> domainList) {
        if (domainList == null || domainList.isEmpty()) {
            return new ArrayList<>();
        }
        return domainList.stream()
                .map(this::convertRevenueDetail)
                .collect(Collectors.toList());
    }

    private CostQuarterlyBudgetRevenueDetail convertRevenueDetail(
            com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetRevenueDetailEntity domainEntity) {
        CostQuarterlyBudgetRevenueDetail infraEntity = new CostQuarterlyBudgetRevenueDetail();
        BeanUtils.copyProperties(domainEntity, infraEntity);
        return infraEntity;
    }
}
