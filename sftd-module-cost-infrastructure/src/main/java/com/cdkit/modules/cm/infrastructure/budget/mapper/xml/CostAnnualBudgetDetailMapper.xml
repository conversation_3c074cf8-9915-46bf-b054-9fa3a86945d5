<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdkit.modules.cm.infrastructure.budget.mapper.CostAnnualBudgetDetailMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE 
		FROM  cost_annual_budget_detail 
		WHERE
			 budget_id = #{mainId} 	</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="com.cdkit.modules.cm.infrastructure.budget.entity.CostAnnualBudgetDetail">
		SELECT *
		FROM  cost_annual_budget_detail
		WHERE
			 budget_id = #{mainId}  and del_flag=0 	</select>

	<select id="selectProjectBudgetByProjectCode" parameterType="java.lang.String" resultType="java.util.Map">
		SELECT
			d.budget_id as annualBudgetId,
			d.annual_budget_code as annualBudgetCode,
			d.wbs_code as wbsCode,
			d.professional_company as professionalCompany,
			d.center as center,
			d.project_name as projectName,
			d.budget_type as budgetType,
			d.revenue_budget as annualRevenueBudget,
			d.direct_cost_budget as annualExpenditureBudget
		FROM cost_annual_budget_detail d
		WHERE d.project_code = #{projectCode}
		  AND d.del_flag = 0
		ORDER BY d.create_time DESC
	</select>
</mapper>
