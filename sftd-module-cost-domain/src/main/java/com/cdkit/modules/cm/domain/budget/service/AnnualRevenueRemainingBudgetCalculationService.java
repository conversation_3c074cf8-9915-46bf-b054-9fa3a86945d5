package com.cdkit.modules.cm.domain.budget.service;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetEntity;
import com.cdkit.modules.cm.domain.budget.repository.CostAnnualBudgetDetailRepository;
import com.cdkit.modules.cm.domain.budget.repository.CostAnnualBudgetRepository;
import com.cdkit.modules.cm.domain.budget.repository.CostQuarterlyBudgetRepository;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectPlanEntity;
import com.cdkit.modules.cm.domain.project.repository.CostProjectPlanRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 年度收入剩余预算计算领域服务
 * 负责计算年度收入剩余预算金额的业务逻辑
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AnnualRevenueRemainingBudgetCalculationService {

    private final CostProjectPlanRepository costProjectPlanRepository;
    private final CostAnnualBudgetRepository costAnnualBudgetRepository;
    private final CostAnnualBudgetDetailRepository costAnnualBudgetDetailRepository;
    private final CostQuarterlyBudgetRepository costQuarterlyBudgetRepository;

    /**
     * 根据季度计划ID计算年度收入剩余预算金额
     * 计算公式：年度收入剩余预算金额 = 年度预算的收入预算金额 - 与该年度预算相关的已审批通过的季度预算的项目预算总额
     *
     * @param quarterlyPlanId 季度计划ID
     * @return 年度收入剩余预算金额（元）
     */
    public BigDecimal calculateAnnualRevenueRemainingBudget(String quarterlyPlanId) {
        if (!StringUtils.hasText(quarterlyPlanId)) {
            throw new IllegalArgumentException("季度计划ID不能为空");
        }

        log.info("开始计算年度收入剩余预算金额，季度计划ID: {}", quarterlyPlanId);

        try {
            // 1. 通过季度计划ID查询季度计划信息
            CostProjectPlanEntity quarterlyPlan = costProjectPlanRepository.getDomainById(quarterlyPlanId);
            if (quarterlyPlan == null) {
                throw new IllegalArgumentException("季度计划不存在，ID: " + quarterlyPlanId);
            }

            // 2. 获取项目编号
            String projectCode = quarterlyPlan.getProjectCode();
            if (!StringUtils.hasText(projectCode)) {
                throw new IllegalArgumentException("季度计划未关联项目，季度计划ID: " + quarterlyPlanId);
            }

            // 3. 通过项目编号查询年度预算明细信息
            List<CostAnnualBudgetDetailRepository.ProjectBudgetInfo> projectBudgetInfoList =
                costAnnualBudgetDetailRepository.findByProjectCode(projectCode);

            if (projectBudgetInfoList == null || projectBudgetInfoList.isEmpty()) {
                throw new IllegalArgumentException("未找到项目对应的年度预算信息，项目编号: " + projectCode);
            }

            // 4. 获取年度预算信息（通过年度预算编码查询）
            CostAnnualBudgetDetailRepository.ProjectBudgetInfo firstBudgetInfo = projectBudgetInfoList.get(0);
            String annualBudgetCode = firstBudgetInfo.getAnnualBudgetCode();

            CostAnnualBudgetEntity annualBudget = costAnnualBudgetRepository.findByBudgetCode(annualBudgetCode);
            if (annualBudget == null) {
                throw new IllegalArgumentException("年度预算不存在，编码: " + annualBudgetCode);
            }

            // 5. 计算该项目在年度预算中的收入预算金额（元）
            BigDecimal annualRevenueBudgetAmount = BigDecimal.ZERO;
            for (CostAnnualBudgetDetailRepository.ProjectBudgetInfo budgetInfo : projectBudgetInfoList) {
                if (budgetInfo.getAnnualRevenueBudget() != null) {
                    annualRevenueBudgetAmount = annualRevenueBudgetAmount.add(budgetInfo.getAnnualRevenueBudget());
                }
            }

            // 6. 查询与该年度预算相关的已审批通过的季度预算列表
            List<CostQuarterlyBudgetEntity> approvedQuarterlyBudgets =
                costQuarterlyBudgetRepository.findByAnnualBudgetIdAndStatus(annualBudget.getBudgetCode(), "LOCKED");

            // 7. 计算已审批季度预算中该项目的预算总额
            BigDecimal approvedQuarterlyBudgetTotal = BigDecimal.ZERO;
            for (CostQuarterlyBudgetEntity quarterlyBudget : approvedQuarterlyBudgets) {
                // 查询季度预算关联的项目计划，检查是否为同一项目
                if (StringUtils.hasText(quarterlyBudget.getQuarterlyPlanNo())) {
                    CostProjectPlanEntity relatedPlan = costProjectPlanRepository.getDomainById(quarterlyBudget.getQuarterlyPlanNo());
                    if (relatedPlan != null && projectCode.equals(relatedPlan.getProjectCode())) {
                        BigDecimal revenueBudgetAmount = quarterlyBudget.getRevenueBudgetAmount();
                        if (revenueBudgetAmount != null) {
                            approvedQuarterlyBudgetTotal = approvedQuarterlyBudgetTotal.add(revenueBudgetAmount);
                        }
                    }
                }
            }

            // 8. 计算年度收入剩余预算金额
            BigDecimal remainingBudgetAmount = annualRevenueBudgetAmount.subtract(approvedQuarterlyBudgetTotal);

            log.info("计算年度收入剩余预算金额完成，季度计划ID: {}, 项目编号: {}, 年度预算收入金额: {}元, 已审批季度预算总额: {}元, 剩余预算金额: {}元",
                    quarterlyPlanId, projectCode, annualRevenueBudgetAmount, approvedQuarterlyBudgetTotal, remainingBudgetAmount);

            return remainingBudgetAmount;

        } catch (Exception e) {
            log.error("计算年度收入剩余预算金额失败，季度计划ID: {}", quarterlyPlanId, e);
            throw e;
        }
    }

    /**
     * 根据季度计划ID计算年度收入剩余预算详细信息
     * 返回包含详细计算信息的结果对象
     *
     * @param quarterlyPlanId 季度计划ID
     * @return 年度收入剩余预算详细信息
     */
    public AnnualRevenueRemainingBudgetInfo calculateAnnualRevenueRemainingBudgetInfo(String quarterlyPlanId) {
        if (!StringUtils.hasText(quarterlyPlanId)) {
            throw new IllegalArgumentException("季度计划ID不能为空");
        }

        log.info("开始计算年度收入剩余预算详细信息，季度计划ID: {}", quarterlyPlanId);

        try {
            // 1. 通过季度计划ID查询季度计划信息
            CostProjectPlanEntity quarterlyPlan = costProjectPlanRepository.getDomainById(quarterlyPlanId);
            if (quarterlyPlan == null) {
                throw new IllegalArgumentException("季度计划不存在，ID: " + quarterlyPlanId);
            }

            // 2. 获取项目编号
            String projectCode = quarterlyPlan.getProjectCode();
            if (!StringUtils.hasText(projectCode)) {
                throw new IllegalArgumentException("季度计划未关联项目，季度计划ID: " + quarterlyPlanId);
            }

            // 3. 通过项目编号查询年度预算明细信息
            List<CostAnnualBudgetDetailRepository.ProjectBudgetInfo> projectBudgetInfoList =
                costAnnualBudgetDetailRepository.findByProjectCode(projectCode);

            if (projectBudgetInfoList == null || projectBudgetInfoList.isEmpty()) {
                throw new IllegalArgumentException("未找到项目对应的年度预算信息，项目编号: " + projectCode);
            }

            // 4. 获取年度预算信息（通过年度预算编码查询）
            CostAnnualBudgetDetailRepository.ProjectBudgetInfo firstBudgetInfo = projectBudgetInfoList.get(0);
            String annualBudgetCode = firstBudgetInfo.getAnnualBudgetCode();

            CostAnnualBudgetEntity annualBudget = costAnnualBudgetRepository.findByBudgetCode(annualBudgetCode);
            if (annualBudget == null) {
                throw new IllegalArgumentException("年度预算不存在，编码: " + annualBudgetCode);
            }

            // 5. 计算该项目在年度预算中的收入预算金额（元）
            BigDecimal annualRevenueBudgetAmount = BigDecimal.ZERO;
            for (CostAnnualBudgetDetailRepository.ProjectBudgetInfo budgetInfo : projectBudgetInfoList) {
                if (budgetInfo.getAnnualRevenueBudget() != null) {
                    annualRevenueBudgetAmount = annualRevenueBudgetAmount.add(budgetInfo.getAnnualRevenueBudget());
                }
            }

            // 6. 查询与该年度预算相关的已审批通过的季度预算列表
            List<CostQuarterlyBudgetEntity> approvedQuarterlyBudgets =
                costQuarterlyBudgetRepository.findByAnnualBudgetIdAndStatus(annualBudget.getBudgetCode(), "LOCKED");

            // 7. 计算已审批季度预算中该项目的预算总额
            BigDecimal approvedQuarterlyBudgetTotal = BigDecimal.ZERO;
            for (CostQuarterlyBudgetEntity quarterlyBudget : approvedQuarterlyBudgets) {
                // 查询季度预算关联的项目计划，检查是否为同一项目
                if (StringUtils.hasText(quarterlyBudget.getQuarterlyPlanNo())) {
                    CostProjectPlanEntity relatedPlan = costProjectPlanRepository.getDomainById(quarterlyBudget.getQuarterlyPlanNo());
                    if (relatedPlan != null && projectCode.equals(relatedPlan.getProjectCode())) {
                        BigDecimal revenueBudgetAmount = quarterlyBudget.getRevenueBudgetAmount();
                        if (revenueBudgetAmount != null) {
                            approvedQuarterlyBudgetTotal = approvedQuarterlyBudgetTotal.add(revenueBudgetAmount);
                        }
                    }
                }
            }

            // 8. 计算年度收入剩余预算金额
            BigDecimal remainingBudgetAmount = annualRevenueBudgetAmount.subtract(approvedQuarterlyBudgetTotal);

            // 8. 构建详细信息对象
            AnnualRevenueRemainingBudgetInfo budgetInfo = new AnnualRevenueRemainingBudgetInfo(
                remainingBudgetAmount,
                annualRevenueBudgetAmount,
                approvedQuarterlyBudgetTotal,
                annualBudget.getBudgetCode(),
                annualBudget.getBudgetName()
            );

            log.info("计算年度收入剩余预算详细信息完成，季度计划ID: {}, 项目编号: {}, 年度预算编码: {}, 剩余预算金额: {}元",
                    quarterlyPlanId, projectCode, annualBudget.getBudgetCode(), remainingBudgetAmount);

            return budgetInfo;

        } catch (Exception e) {
            log.error("计算年度收入剩余预算详细信息失败，季度计划ID: {}", quarterlyPlanId, e);
            throw e;
        }
    }

    /**
     * 年度收入剩余预算计算结果信息
     */
    public static class AnnualRevenueRemainingBudgetInfo {
        /**年度收入剩余预算金额（元）*/
        private BigDecimal annualRevenueRemainingBudgetAmount;
        
        /**年度预算收入金额（元）*/
        private BigDecimal annualRevenueBudgetAmount;
        
        /**已审批季度预算总额（元）*/
        private BigDecimal approvedQuarterlyBudgetTotal;
        
        /**年度预算编码*/
        private String annualBudgetCode;
        
        /**年度预算名称*/
        private String annualBudgetName;

        public AnnualRevenueRemainingBudgetInfo(BigDecimal annualRevenueRemainingBudgetAmount,
                                              BigDecimal annualRevenueBudgetAmount,
                                              BigDecimal approvedQuarterlyBudgetTotal,
                                              String annualBudgetCode,
                                              String annualBudgetName) {
            this.annualRevenueRemainingBudgetAmount = annualRevenueRemainingBudgetAmount;
            this.annualRevenueBudgetAmount = annualRevenueBudgetAmount;
            this.approvedQuarterlyBudgetTotal = approvedQuarterlyBudgetTotal;
            this.annualBudgetCode = annualBudgetCode;
            this.annualBudgetName = annualBudgetName;
        }

        // Getters
        public BigDecimal getAnnualRevenueRemainingBudgetAmount() {
            return annualRevenueRemainingBudgetAmount;
        }

        public BigDecimal getAnnualRevenueBudgetAmount() {
            return annualRevenueBudgetAmount;
        }

        public BigDecimal getApprovedQuarterlyBudgetTotal() {
            return approvedQuarterlyBudgetTotal;
        }

        public String getAnnualBudgetCode() {
            return annualBudgetCode;
        }

        public String getAnnualBudgetName() {
            return annualBudgetName;
        }
    }
}
